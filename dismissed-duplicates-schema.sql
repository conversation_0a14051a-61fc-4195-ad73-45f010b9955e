-- Dismissed Duplicates Schema for Supabase
-- Run this in your Supabase SQL Editor to create the dismissed_duplicates table

-- Enable UUID extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create dismissed_duplicates table
CREATE TABLE IF NOT EXISTS dismissed_duplicates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    duplicate_id VARCHAR(255) NOT NULL UNIQUE, -- The stable duplicate ID from detection service
    dismissed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_dismissed_duplicates_duplicate_id ON dismissed_duplicates(duplicate_id);
CREATE INDEX IF NOT EXISTS idx_dismissed_duplicates_dismissed_at ON dismissed_duplicates(dismissed_at);

-- Add comments for documentation
COMMENT ON TABLE dismissed_duplicates IS 'Stores dismissed duplicate client notifications to persist across browser sessions';
COMMENT ON COLUMN dismissed_duplicates.duplicate_id IS 'The stable duplicate ID generated by DuplicateDetectionService (e.g., dup-uuid1-uuid2)';
COMMENT ON COLUMN dismissed_duplicates.dismissed_at IS 'When the duplicate was dismissed by the user';

-- Verify the table was created successfully
SELECT 'Dismissed duplicates table created successfully!' as status;
