@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Mobile-first responsive design */
.safe-area-pt {
  padding-top: 12px;
}

.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-pl {
  padding-left: env(safe-area-inset-left);
}

.safe-area-pr {
  padding-right: env(safe-area-inset-right);
}

/* PWA specific styles */
@media (display-mode: standalone) {
  body {
    /* Hide scrollbars in standalone mode for a more app-like feel */
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus in iOS */
  input, textarea, select {
    font-size: 16px !important;
  }
}

/* Prevent text selection on UI elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Touch-friendly button styles */
.touch-button {
  min-height: 44px;
  min-width: 44px;
}

/* Smooth scrolling for better mobile experience */
html {
  scroll-behavior: smooth;
}

/* Hide address bar on mobile when in standalone mode */
@media (display-mode: standalone) {
  html, body {
    height: 100vh;
    overflow-x: hidden;
  }
}

/* Remove webkit scrollbar completely */
::-webkit-scrollbar {
  width: 0px !important;
  background: transparent !important;
}

::-webkit-scrollbar-track {
  background: transparent !important;
}

::-webkit-scrollbar-thumb {
  background: transparent !important;
}


/* Custom scrollbar styles for calendar day columns (desktop only) */
@media (min-width: 768px) {
  .calendar-day-scroll {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
  }

  .calendar-day-scroll::-webkit-scrollbar {
    width: 6px !important;
    background: transparent !important;
  }

  .calendar-day-scroll::-webkit-scrollbar-track {
    background: transparent !important;
  }

  .calendar-day-scroll::-webkit-scrollbar-thumb {
    background-color: #d1d5db !important;
    border-radius: 3px;
  }

  .calendar-day-scroll::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af !important;
  }
}

/* FAB Animation */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}


